
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**


**Panneau de contrôle arrière-plan pour simuler les modes de transition entre la nuit profonde jusqu'au crépuscule**
Nous allons régler correctement les phases de transition au clic événement sur chaque bouton `NuitProfonde`, `<PERSON><PERSON>`, `LeverDeSoleil`, `<PERSON><PERSON>`, `<PERSON><PERSON>`, `<PERSON><PERSON><PERSON>`, `AprèsMidi`, `CoucherDeSoleil`, `Crépuscule`. 
Components\UI\TimeSimulator.tsx


**Ci-desso<PERSON>, le fichier qui contrôle la dérive du soleil au clic événement sur chaque bouton du contrôle arrière-plan**
Components\Background\SunriseAnimation.tsx


**ci-dessous les répertoires où sont stockés les sons, effects pour les boutons au clic événement**
public\sounds
public\sounds\apres-midi
public\sounds\aube
public\sounds\coucher-soleil
public\sounds\crepuscule
public\sounds\lever-soleil
public\sounds\matin
public\sounds\midi
public\sounds\nuit-profonde

**Ici dessous, les répertoires avec les fichiers respectifs qui contrôlent l'audio.**
Components\Audio
Components\Audio\AmbientSoundManager.tsx
Components\Audio\AudioControlPanel.tsx


Commençons par examiner avec le fichier `TimeSimulator` le panneau de contrôle arrière-plan lorsqu'on clique sur le bouton Aube, Il faut coordonner les transitions, que tout soit harmonieux et parfaitement synchronisé Il y a un dysfonctionnement si l'on clique sur le bouton matin et qu'on revient sur le bouton aube, le soleil est toujours au-dessus de l'horizon. Alors que le soleil devrait être sous l'horizon. 

Au niveau des sons, même chose, cela doit toujours fonctionner au One-Click Button Event. Si je clique sur Aube, ça doit faire une transition sur les fichiers audio afin de les activer dans le mode Aube, mais tous les autres modes doivent s'estomper automatiquement.

OneClickButton, Levé de soleil. Le soleil n'est pas assez haut. Il faudrait le positionner un peu plus haut, entre 15 et 20 degrés supplémentaires au-dessus de l'horizon, du background. 

Pour faire simple, il faut tout le temps synchroniser le tout. Je m'explique. Il faut que les dégradés, quand on clique sur un bouton, un mode, que ce soit aube, lever de soleil ou matin, etc. etc. Les modes de transition du dégradé, s'ils sont réglés, admettons, sur 15 secondes, le soleil doit être réglé de la même manière. Le soleil doit se déplacer de 15 secondes. Pourquoi ? Pour que tout cela se synchronise et soit harmonieux. Il ne faut pas que le soleil soit plus rapide que le dégradé. Pour résumer, si vous avez un dégradé transitoire au niveau de la couleur qui va durer 15 secondes, le soleil lui aussi, lorsqu'on clique sur, par exemple, le bouton lever de soleil, pour réaliser sa course à un endroit précis, puis il monte dans le ciel, il doit aussi parcourir un laps de temps de 15 secondes pour pouvoir Se déplacer . 

**Clic événement sur le bouton matin**
Le soleil monte beaucoup trop vite au-dessus de l'horizon. Même chose, si vous mettez le dégradé transitoire pour changer de couleur sur 15 secondes, il faudra faire de même avec le soleil. Le soleil doit, d'une position précise à une autre, se déplacer, faire tout le trajet en 15 secondes 

**Clic sur le bouton Midi Zenith**
Grosse correction à faire. Lorsqu'on clique sur le bouton 12 h Zénith, déjà les nuages sont noirs. Ils deviennent blancs 10-15 secondes après. Il faut corriger cela. Puis le soleil part dans la mauvaise direction. Il part sur la droite alors qu'il doit suivre une trajectoire parabolique. Il doit se déplacer légèrement en forme de courbe sur la gauche en haut. Et normalement le soleil doit être au plus haut, tout en haut quasiment de l'écran. Profitez aussi pendant son parcours, quand il se déplace, vous pouvez atténuer tout doucement, progressivement, son halo jaune. 

**Événement au clic bouton pour le mode après-midi**
Dans ce mode, même chose, correction sur les nuages. Les nuages doivent être blancs dès le départ. Alors que là, il y a une transition, on ne sait pas pourquoi. Ils sont noirs puis deviennent blancs au bout de quelques secondes. Ce n'est pas bon, il faut corriger. Ils doivent rester blancs. Puis apporter une correction aussi au niveau du soleil. Le soleil n'est pas du tout à la bonne position. Si le mode d'avant était midi zénith, donc le soleil doit légèrement entamer une descente parabolique, mais dans le sens contraire. Le soleil se dirige légèrement en bas sur la droite en respectant cette courbe pour simuler l'après-midi. Je vous rappelle les modes transitoires. Si vous mettez 15 secondes au niveau du dégradé, le soleil doit se déplacer aussi de 15 secondes pour qu'il soit synchro avec le dégradé en couleur dans sa transition. 


**Événement au clic bouton pour le mode crépuscule**
Même chose pour le soleil, sa position n'est pas bien respectée, on voit encore quelques rayons. Pour le crépuscule, normalement le soleil est complètement derrière l'horizon, donc il doit passer derrière le background. Donc vous pouvez baisser sa position en fin de course de 15, 20, voire 25 degrés. 

**Événements au clic bouton pour le mode Nuit Profonde**
Lors du clic bouton sur `Nuit Profonde`, la transition au niveau de la couleur des nuages ne démarre pas tout de suite. Il faut qu'elle soit très progressive, quasiment synchronisée avec le dégradé qui simule la nuit profonde. Vérifiez aussi le nom, le son de la chouette. Elle est incluse dans le mode nuit profonde, mais lorsqu'on clique sur un autre mode, elle doit s'arrêter ou s'estomper comme vous voulez. Et ce n'est pas le cas. Si on clique sur d'autres modes, on entend toujours la chouette. Même chose au niveau de la position du soleil, sur nuit profonde, on aperçoit encore quelques rayons qui passent un petit peu au-dessus de l'horizon. Il faut le descendre beaucoup plus bas sur l'horizon pour la position du soleil. 




**Instructions supplémentaires de la part de Cisco pendant la réalisation des tâches**

Je rédigerai des notes supplémentaires pendant vos tâches. Donc surveillez bien ce fichier 




















