import React, { useLayoutEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { gsap } from 'gsap';

// Interface pour les méthodes exposées du composant
export interface SunriseAnimationRef {
  triggerSunrise: () => void;
  triggerMorning: () => void; // CISCO: Nouvelle méthode pour l'animation du matin
  triggerMidday: () => void; // CISCO: Animation pour le zénith 12h
  triggerAfternoon: () => void; // CISCO: Animation pour l'après-midi 15h
  triggerSunset: () => void; // CISCO: Animation pour le coucher 18h
  triggerDawn: () => void; // CISCO: Animation pour l'aube (soleil sous horizon)
  triggerDusk: () => void; // CISCO: Animation pour le crépuscule (soleil derrière horizon)
  triggerNight: () => void; // CISCO: Animation pour la nuit profonde (soleil très bas)
  resetSun: () => void;
}

// Interface pour les props du composant
interface SunriseAnimationProps {
  isVisible: boolean; // Contrôle la visibilité du composant
}

const SunriseAnimation = forwardRef<SunriseAnimationRef, SunriseAnimationProps>(
  ({ isVisible }, ref) => {
    // Références pour les éléments DOM - VERSION SIMPLIFIÉE
    const containerRef = useRef<HTMLDivElement>(null);
    const sunWrapperRef = useRef<HTMLDivElement>(null);
    const sunGlowRef = useRef<HTMLDivElement>(null);
    const lensFlareRef = useRef<HTMLDivElement>(null);
    const sunImageRef = useRef<HTMLImageElement>(null);

    // Référence pour la timeline GSAP
    const timelineRef = useRef<gsap.core.Timeline | null>(null);

    // 🌅 FONCTION PRINCIPALE: Déclencher l'animation de lever de soleil - VERSION SIMPLIFIÉE
    const triggerSunrise = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌅 Éléments DOM non prêts pour l\'animation');
        return;
      }

      console.log('🌅 Déclenchement de l\'animation de lever de soleil - Version réaliste');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation de lever de soleil terminée');
        }
      });

      // Position initiale - soleil simple
      gsap.set(sunWrapperRef.current, {
        y: '60%',
        opacity: 1
      });
      gsap.set(sunGlowRef.current, {
        opacity: 0,
        scale: 0.8
      });
      gsap.set(lensFlareRef.current, {
        opacity: 0,
        rotation: 0 // CISCO: Position initiale pour rotation (plus besoin de y car dans le même conteneur)
      });

      // CISCO: PHASE 1 - Le soleil monte 15-20° au-dessus de l'horizon (SYNCHRONISÉ 15s)
      timelineRef.current.fromTo(
        sunWrapperRef.current,
        { y: '60%' }, // Position sous l'horizon
        {
          y: '-15%', // CISCO: 15-20° au-dessus de l'horizon (ajusté selon vos specs)
          duration: 15.0, // CISCO: SYNCHRONISATION PARFAITE - 15 secondes
          ease: 'power1.out'
        },
        0
      );

      // CISCO: PHASE 2 - La lueur apparaît progressivement (SYNCHRONISÉ 15s)
      timelineRef.current.fromTo(
        sunGlowRef.current,
        { opacity: 0, scale: 0.8 },
        {
          opacity: 1.0, // CISCO: Opacité maximale pour visibilité
          scale: 1.2,   // CISCO: Plus grand pour plus d'impact
          duration: 12.0, // CISCO: Synchronisé avec l'animation principale
          ease: 'power2.out'
        },
        2 // Démarre après 2 secondes
      );

      // PHASE 3: Le lens flare apparaît - CISCO: Plus besoin d'animation de position car dans le même conteneur !
      timelineRef.current.fromTo(
        lensFlareRef.current,
        {
          opacity: 0,
          rotation: 0
        },
        {
          opacity: 0.7, // CISCO: Visible mais subtil pour l'illusion
          duration: 8.0, // CISCO: Apparition progressive
          ease: 'power2.out'
        },
        2 // CISCO: Démarre après 2 secondes
      );

      // PHASE 4: Rotation TRÈS LENTE continue du lens-flare - CISCO: Rayons qui tournent
      timelineRef.current.to(
        lensFlareRef.current,
        {
          rotation: 360,
          duration: 120.0, // CISCO: 2 minutes pour une rotation complète = TRÈS LENT
          ease: 'none',
          repeat: -1 // CISCO: Rotation infinie pour l'illusion des rayons
        },
        4 // CISCO: Démarre la rotation après 4 secondes (quand le lens-flare est visible)
      );
    };

    // 🌄 NOUVELLE FONCTION: Animation pour le mode MATIN (9h) - CISCO: Soleil plus haut et à gauche
    const triggerMorning = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌄 Éléments DOM non prêts pour l\'animation du matin');
        return;
      }

      console.log('🌄 Déclenchement de l\'animation du matin - Soleil en position 9h avec courbe vers la gauche');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation du matin terminée - Soleil en position 9h');
        }
      });

      // CISCO: Position initiale = POSITION FINALE du lever de soleil (continuité !)
      // Pas de gsap.set() car on continue depuis la position actuelle !

      // CISCO: PHASE 1 - Le soleil continue sa course vers la GAUCHE et PLUS HAUT (SYNCHRONISÉ 15s)
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: '-85%', // CISCO: Position matin 9h - entre lever et zénith
          x: '-35%', // CISCO: Courbe vers la gauche (trajectoire parabolique naturelle)
          duration: 15.0, // CISCO: SYNCHRONISATION PARFAITE - 15 secondes
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 2 - La lueur s'intensifie pour le matin (SYNCHRONISÉ 15s)
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: 1.3, // CISCO: Plus lumineux pour le matin actif
          scale: 1.4,   // CISCO: Plus grand pour plus de présence
          duration: 12.0, // CISCO: Synchronisé avec l'animation principale
          ease: 'power2.out'
        },
        2 // Démarre après 2 secondes
      );

      // PHASE 3: Le lens flare CONTINUE et s'intensifie - CISCO: Depuis son état actuel
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: 0.8, // CISCO: Plus visible pour le matin lumineux
          duration: 10.0,
          ease: 'power2.out'
        },
        3 // CISCO: Démarre après 3 secondes
      );

      // PHASE 3B: RÉDUCTION du lens-flare - CISCO: Plus le soleil monte, plus les rayons raccourcissent !
      timelineRef.current.to(
        lensFlareRef.current,
        {
          scale: 0.7, // CISCO: Réduction à 70% de la taille originale (rayons plus courts)
          duration: 12.0, // CISCO: Réduction progressive pendant la montée
          ease: 'power2.out'
        },
        4 // CISCO: Démarre après 4 secondes, en parallèle avec la montée
      );

      // PHASE 4: Rotation continue du lens-flare - CISCO: Plus rapide pour le matin actif
      timelineRef.current.to(
        lensFlareRef.current,
        {
          rotation: 360,
          duration: 90.0, // CISCO: Plus rapide que le lever de soleil (90s vs 120s)
          ease: 'none',
          repeat: -1
        },
        5 // CISCO: Démarre après 5 secondes
      );
    };

    // ☀️ NOUVELLE FONCTION: Animation pour le ZÉNITH (12h) - CISCO: Soleil au plus haut point
    const triggerMidday = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('☀️ Éléments DOM non prêts pour l\'animation du zénith');
        return;
      }

      console.log('☀️ Déclenchement de l\'animation du zénith - Soleil au plus haut point (12h)');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation du zénith terminée - Soleil au point culminant');
        }
      });

      // CISCO: PHASE 1 - Le soleil atteint le ZÉNITH (TOUT EN HAUT - SYNCHRONISÉ 15s)
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: '-120%', // CISCO: POSITION MAXIMALE - Tout en haut de l'écran selon vos specs
          x: '0%', // CISCO: Centré parfaitement au zénith
          duration: 15.0, // CISCO: SYNCHRONISATION PARFAITE - 15 secondes
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 2 - Lueur maximale au zénith puis ATTÉNUATION progressive du halo
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: 1.5, // CISCO: Lueur maximale au zénith
          scale: 1.6,   // CISCO: Plus grand au point culminant
          duration: 8.0, // Montée vers le maximum
          ease: 'power2.out'
        },
        2
      );

      // CISCO: PHASE 2B - ATTÉNUATION progressive du halo jaune (selon vos specs)
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: 1.2, // CISCO: Atténuation progressive du halo
          scale: 1.4,   // CISCO: Réduction de la taille du halo
          duration: 5.0, // CISCO: Atténuation sur les dernières secondes
          ease: 'power2.inOut'
        },
        10 // Démarre après 10 secondes (fin de montée)
      );

      // PHASE 3: Lens flare au maximum
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: 1.0, // CISCO: Visibilité maximale au zénith
          scale: 0.6,   // CISCO: Rayons plus courts au zénith (soleil haut)
          duration: 8.0,
          ease: 'power2.out'
        },
        3
      );
    };

    // 🌅 NOUVELLE FONCTION: Animation pour l'APRÈS-MIDI (15h) - CISCO: Début de la descente vers la droite
    const triggerAfternoon = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌅 Éléments DOM non prêts pour l\'animation de l\'après-midi');
        return;
      }

      console.log('🌅 Déclenchement de l\'animation de l\'après-midi - Soleil commence sa descente vers la droite');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation de l\'après-midi terminée - Soleil en descente');
        }
      });

      // CISCO: PHASE 1 - Descente parabolique depuis le zénith vers la DROITE (SYNCHRONISÉ 15s)
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: '-85%', // CISCO: Descente depuis le zénith (-120% → -85%) - Position après-midi
          x: '+35%', // CISCO: Courbe parabolique vers la DROITE (trajectoire naturelle)
          duration: 15.0, // CISCO: SYNCHRONISATION PARFAITE - 15 secondes
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 2 - Lueur qui diminue légèrement (SYNCHRONISÉ)
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: 1.2, // CISCO: Légèrement moins lumineux qu'au zénith
          scale: 1.4,   // CISCO: Légèrement plus petit
          duration: 12.0, // CISCO: Synchronisé avec l'animation principale
          ease: 'power2.out'
        },
        2 // Démarre après 2 secondes
      );

      // PHASE 3: Lens flare qui s'agrandit (soleil plus bas)
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: 0.8, // CISCO: Toujours visible
          scale: 0.7,   // CISCO: Rayons qui s'allongent (soleil plus bas)
          duration: 10.0,
          ease: 'power2.out'
        },
        3
      );
    };

    // 🌇 NOUVELLE FONCTION: Animation pour le COUCHER (18h) - CISCO: Soleil bas vers l'horizon droit
    const triggerSunset = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌇 Éléments DOM non prêts pour l\'animation du coucher');
        return;
      }

      console.log('🌇 Déclenchement de l\'animation du coucher - Soleil vers l\'horizon droit');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation du coucher terminée - Soleil à l\'horizon');
        }
      });

      // CISCO: PHASE 1 - Le soleil DESCEND vers l'horizon DROIT (SYNCHRONISÉ 15s)
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: '50%', // CISCO: Position à l'horizon droit (légèrement visible)
          x: '+45%', // CISCO: Complètement à droite (inverse du lever à gauche)
          duration: 15.0, // CISCO: SYNCHRONISATION PARFAITE - 15 secondes
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 2 - Lueur intense du coucher de soleil (SYNCHRONISÉ)
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: 1.3, // CISCO: Lueur intense du coucher
          scale: 1.5,   // CISCO: Grande lueur à l'horizon
          duration: 12.0, // CISCO: Synchronisé avec l'animation principale
          ease: 'power2.out'
        },
        2 // Démarre après 2 secondes
      );

      // PHASE 3: Lens flare maximal (soleil bas = rayons longs)
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: 0.9, // CISCO: Très visible au coucher
          scale: 1.0,   // CISCO: Rayons longs à l'horizon
          duration: 12.0,
          ease: 'power2.out'
        },
        3
      );
    };

    // 🔄 FONCTION: Remettre le soleil en position initiale - VERSION SIMPLIFIÉE
    const resetSun = () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      if (sunWrapperRef.current && sunGlowRef.current && lensFlareRef.current) {
        gsap.set(sunWrapperRef.current, {
          y: '60%',
          x: '0%', // CISCO: Reset aussi la position horizontale
          opacity: 1
        });
        gsap.set(sunGlowRef.current, {
          opacity: 0,
          scale: 0.8
        });
        gsap.set(lensFlareRef.current, {
          opacity: 0,
          rotation: 0 // CISCO: Reset rotation (plus besoin de y car dans le même conteneur)
        });
      }

      console.log('🔄 Soleil remis en position initiale');
    };

    // 🌅 CISCO: NOUVELLE FONCTION - Animation pour le mode AUBE (soleil SOUS l'horizon)
    const triggerDawn = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌅 Éléments DOM non prêts pour l\'animation de l\'aube');
        return;
      }

      console.log('🌅 Déclenchement de l\'animation de l\'aube - Soleil SOUS l\'horizon');

      // Nettoyer l'animation précédente
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation de l\'aube terminée - Soleil sous l\'horizon');
        }
      });

      // CISCO: PHASE 1 - Positionner le soleil SOUS l'horizon (invisible)
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: '80%', // CISCO: Bien sous l'horizon (invisible)
          x: '-60%', // CISCO: Position Est (gauche) pour préparer le lever
          duration: 15.0, // CISCO: SYNCHRONISATION PARFAITE - 15 secondes
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 2 - Éteindre complètement la lueur
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: 0, // CISCO: Pas de lueur visible à l'aube
          scale: 0.8,
          duration: 15.0,
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 3 - Arrêter le lens-flare
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: 0, // CISCO: Pas de rayons visibles
          duration: 15.0,
          ease: 'power2.out'
        },
        0
      );
    };

    // 🌆 CISCO: NOUVELLE FONCTION - Animation pour le mode CRÉPUSCULE (soleil derrière l'horizon)
    const triggerDusk = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌆 Éléments DOM non prêts pour l\'animation du crépuscule');
        return;
      }

      console.log('🌆 Déclenchement de l\'animation du crépuscule - Soleil derrière l\'horizon');

      // Nettoyer l'animation précédente
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation du crépuscule terminée - Soleil derrière l\'horizon');
        }
      });

      // CISCO: PHASE 1 - Positionner le soleil DERRIÈRE l'horizon (15-25° plus bas)
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: '85%', // CISCO: 15-25° plus bas que le coucher (complètement invisible)
          x: '+60%', // CISCO: Position Ouest (droite) après le coucher
          duration: 15.0, // CISCO: SYNCHRONISATION PARFAITE - 15 secondes
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 2 - Éteindre progressivement la lueur
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: 0, // CISCO: Plus de lueur visible au crépuscule
          scale: 0.8,
          duration: 15.0,
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 3 - Éliminer tous les rayons
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: 0, // CISCO: Plus de rayons visibles
          duration: 15.0,
          ease: 'power2.out'
        },
        0
      );
    };

    // 🌌 CISCO: NOUVELLE FONCTION - Animation pour le mode NUIT PROFONDE (soleil très bas)
    const triggerNight = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌌 Éléments DOM non prêts pour l\'animation de la nuit profonde');
        return;
      }

      console.log('🌌 Déclenchement de l\'animation de la nuit profonde - Soleil très bas');

      // Nettoyer l'animation précédente
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation de la nuit profonde terminée - Soleil très bas');
        }
      });

      // CISCO: PHASE 1 - Positionner le soleil TRÈS BAS (aucun rayon visible)
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: '100%', // CISCO: Position la plus basse possible (complètement invisible)
          x: '0%', // CISCO: Position centrale (milieu de la nuit)
          duration: 15.0, // CISCO: SYNCHRONISATION PARFAITE - 15 secondes
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 2 - Éteindre complètement la lueur
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: 0, // CISCO: Aucune lueur en nuit profonde
          scale: 0.8,
          duration: 15.0,
          ease: 'power2.out'
        },
        0
      );

      // CISCO: PHASE 3 - Éliminer tous les rayons
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: 0, // CISCO: Aucun rayon visible
          duration: 15.0,
          ease: 'power2.out'
        },
        0
      );
    };

    // Exposer les méthodes via useImperativeHandle
    useImperativeHandle(ref, () => ({
      triggerSunrise,
      triggerMorning, // CISCO: Animation pour le mode matin 9h
      triggerMidday, // CISCO: Animation pour le zénith 12h
      triggerAfternoon, // CISCO: Animation pour l'après-midi 15h
      triggerSunset, // CISCO: Animation pour le coucher 18h
      triggerDawn, // CISCO: Animation pour l'aube (soleil sous horizon)
      triggerDusk, // CISCO: Animation pour le crépuscule (soleil derrière horizon)
      triggerNight, // CISCO: Animation pour la nuit profonde (soleil très bas)
      resetSun
    }));

    // Cleanup à la destruction du composant
    useLayoutEffect(() => {
      return () => {
        if (timelineRef.current) {
          timelineRef.current.kill();
        }
      };
    }, []);

    // Ne pas rendre si non visible
    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={containerRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 1.8 }} // 🔧 CISCO: Soleil reste derrière les collines (z-index 5) - NE PAS TOUCHER
      >
        {/* Conteneur pour le soleil et ses effets - CISCO: Soleil AGRANDI avec lens-flare intégré */}
        <div
          ref={sunWrapperRef}
          className="absolute w-52 h-52 left-1/2 top-1/2 -translate-x-1/2"
          style={{
            transform: 'translateX(-50%) translateY(60%)', // Position initiale plus haute (100px de moins)
          }}
        >
          <div className="relative w-full h-full">
            {/* EFFET 1: Lueur subtile du soleil */}
            <div
              ref={sunGlowRef}
              className="sun-glow absolute inset-0 opacity-0"
            />

            {/* EFFET 2: Lens Flare PNG - CISCO: Maintenant DANS le conteneur du soleil pour synchronisation parfaite */}
            <div
              ref={lensFlareRef}
              className="absolute opacity-0 pointer-events-none"
              style={{
                width: '600px',   // CISCO: Plus grand pour couvrir le soleil agrandi
                height: 'auto',   // CISCO: Garde les proportions
                left: '50%',      // CISCO: Centré sur le soleil
                top: '50%',       // CISCO: Centré sur le soleil
                transform: 'translate(-50%, -50%)',
                transformOrigin: 'center center', // CISCO: Rotation autour du centre
                zIndex: 15 // CISCO: Au-dessus du soleil pour les rayons
              }}
            >
              <img
                src="/lens-flare-light-3508x2540.png"
                alt="Lens Flare"
                className="w-full h-auto"
                style={{
                  mixBlendMode: 'screen' // CISCO: Élimine le fond noir
                }}
              />
            </div>

            {/* L'image du soleil - CISCO: BEAUCOUP plus lumineux avec lueur diffuse */}
            <img
              ref={sunImageRef}
              src="/SUN.png"
              alt="Soleil"
              className="relative z-10 w-full h-full object-contain"
              style={{
                filter: 'brightness(2.5) contrast(1.5) saturate(1.6) drop-shadow(0 0 40px rgba(255, 255, 255, 1.0)) drop-shadow(0 0 80px rgba(255, 220, 0, 0.8)) drop-shadow(0 0 120px rgba(255, 200, 0, 0.6))'
              }}
            />
          </div>
        </div>
      </div>
    );
  }
);

SunriseAnimation.displayName = 'SunriseAnimation';

export default SunriseAnimation;
